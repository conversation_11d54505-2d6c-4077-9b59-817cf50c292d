/*
 * @LastEditors: <PERSON>shi <EMAIL>
 * @Date: 2025-08-22 20:36:01
 * @LastEditTime: 2025-08-22 20:37:30
 * @FilePath: /逆向百例/福建省公共资源交易电子公共服务平台/curl.js
 */
const axios = require('axios');
async function req(){
    const response = await axios.post(
  'https://ggzyfw.fujian.gov.cn/FwPortalApi/Trade/TradeInfo',
  {
    'pageNo': 4,
    'pageSize': 20,
    'total': 3004,
    'AREACODE': '',
    'M_PROJECT_TYPE': '',
    'KIND': 'GCJS',
    'GGTYPE': '1',
    'PROTYPE': '',
    'timeType': '6',
    'BeginTime': '2025-02-22 00:00:00',
    'EndTime': '2025-08-22 23:59:59',
    'createTime': '',
    'ts': 1755866130230
  },
  {
    headers: {
      'Accept': 'application/json, text/plain, */*',
      'Accept-Language': 'zh-CN,zh;q=0.9',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Content-Type': 'application/json;charset=UTF-8',
      'Origin': 'https://ggzyfw.fujian.gov.cn',
      'Pragma': 'no-cache',
      'Referer': 'https://ggzyfw.fujian.gov.cn/business/list/',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'same-origin',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/141.0.0.0 Safari/537.36',
      'dnt': '1',
      'portal-sign': '49d44c39dd81e97b43d6423e800675a5',
      'sec-ch-ua': '"Google Chrome";v="141", "Not?A_Brand";v="8", "Chromium";v="141"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-gpc': '1'
    }
  }
);
const {Data}=response.data;
console.log(Data);
}
req()