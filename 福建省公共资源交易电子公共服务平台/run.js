/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-22 19:48:25
 * @LastEditTime: 2025-08-22 20:45:11
 * @FilePath: /逆向百例/福建省公共资源交易电子公共服务平台/run.js
 */
const CryptoJS = require("crypto-js");
let e={
    "ts": 1755863363700,
    "pageNo": 2,
    "pageSize": 20,
    "total": 2999,
    "AREACODE": "",
    "M_PROJECT_TYPE": "",
    "KIND": "GCJS",
    "GGTYPE": "1",
    "PROTYPE": "",
    "timeType": "6",
    "BeginTime": "2025-02-22 00:00:00",
    "EndTime": "2025-08-22 23:59:59",
    "createTime": ""
}
    function l(t, e) {
            return t.toString().toUpperCase() > e.toString().toUpperCase() ? 1 : t.toString().toUpperCase() == e.toString().toUpperCase() ? 0 : -1
        }
        function u(t) {
            for (var e = Object.keys(t).sort(l), n = "", a = 0; a < e.length; a++)
                if (void 0 !== t[e[a]])
                    if (t[e[a]] && t[e[a]]instanceof Object || t[e[a]]instanceof Array) {
                        var i = JSON.stringify(t[e[a]]);
                        n += e[a] + i
                    } else
                        n += e[a] + t[e[a]];
            return n
        }
        function s(e){
            return CryptoJS.MD5(e).toString()
        }

  function d(t) {
            for (var e in t)
                "" !== t[e] && void 0 !== t[e] || delete t[e];
            var n = "B3978D054A72A7002063637CCDF6B2E5"+ u(t);
            return s(n).toLocaleLowerCase()
        }
function getSign(e){
    return d(e)
}
console.log(getSign(e));
const config = {
    e: 'EB444973714E4A40876CE66BE45D5930', // AES 密钥 (hex)
    i: 'B5A8904209931867'                  // IV (hex)
};

      function decrypt(t) {
            const key = CryptoJS.enc.Hex.parse(config.e); // 正确：hex -> WordArray
    const iv = CryptoJS.enc.Hex.parse(config.i);  // 正确：hex -> WordArray

    const decrypted = CryptoJS.AES.decrypt(t, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });

    return decrypted.toString(CryptoJS.enc.Utf8);
        }
data="QmpMHilqO4hqXHd8l/5JJTklVmqFUJexCWZR5E5EJ4OlSQvbqkyMO9sgNqtSNBf99AFs+TdUehG8WWjXOR6H4NOb/A2JrmA6HapXbJ1SYc8yNz5lG3p8e28CQo6SMQnUihqz4f7KYwQdhW6r+VWvbeRidfahyepMYPuasFY5o+XWjm5MQUkwa/Y36qzu5wl84pP+uQNoJId5r/8dB4tLmEP2udMG7UkK3FXVUcTld7B7QjgzXarwuFXOlNTU1TBby5mqKUTaoRVoLYpLzTtnQvCyo3eSnDE0xHcf1OrQBLCQ4sZvdgCN1iPpyzNIDapl0j3+iThY+SJeGvuMNbcTewuXbcC7NV4ckK6/gXQAc9E2cbrJMfMZp0ZhaMsIMzDGv3ia4c/PcbkpUokyH793cZ/PRjgX8+4feQcRDH9jiDnR1/5k117UD/rd/I4uaxqu0bitk1R+mrnyh7jZxXeoAvFPmLfY4R6643F+bjqTigYhnicd2pu3j3Zuv5GMeZMrDuIkZTI4fs7y+zS7vZ91CuZ8QNJjGJNoBbtnd5ppCgtult5D9ZLgx32tUsQW5CzON9ZdxpRIUrcxjUQJVTwoI2b/1BRJzMpEWz9oPe0wlhK74hpYmI+jdrEKH3VUTuiZZ/Wvehha8fn3F3ffrv2G2u3LGJxIkDZ46875lTGytIZTjcCIKLIGtwb2+cJcTdllKexbunewKbUHDc3/M8bLlmhf9e0lamuklL8gmw9gDTy4S8l1HHUs3K5q0UwxbQLBbVLKJE6K/XYVHWr9KxnITzbGPGOjobWH7xcQ+XM9iI+/mmEIndkQjGkpqA9h6ZfsgFyucWVyJSEnENIUA2h/G2yGB3ROkKd96vaqv67mDtKx3J5rCHeR2C5CgIqDuiw43c42T3DRE2iMe49YqKJE1LzpmXa2DGobDo9L/9RjAUwfQ7SDUMEnh3AytZN0TTQUetniaB6+zURTKptmclQ2vjhirUAq6B7QOsxX8ceoIULARXQrTJ3jX+Ss3eXjqiJb9JSQ1YASKY5VZd+1n5NS77m6BFm/Qsi+0FNvwRM79Tu2T9DtVWyliEgNXK6XYl6sHsbmOEJyqhTy3YTD4NbrYNAx4qO1YbrCFLEF9Pakg29UUUzKxWgbRNPnjfTmaSz0kzd15ICnxNY9j/Mm5zjAPNn8z66/UUa8O11iCPGIvVtAWQj09O8eBLguRKbfHjNN1N9+oKY5bfPGdJP5bAcv7G3UsasFmzn+efoxI1v0X3Tbg1I6C3NTAJKSTw31O58VGxLlM9I4kJ5YswQVc06U5QlbjoSsNn42/pJkSTueQ8YlTR7IxZBdgi4SoLPGVDHqylHGf40m/ooGZvlI1fzh/iqXEmFr4tQEntv4u6NFQ/qn45KYwk2tEfCYTVjLWfsBJn+iGWUcB6HCzb41dGnJmbBs4tXTPg2dvIWLzNPLCZ+fdHWT90YVaI+ff3ll6JuKO04DEsbNPfTWYyFT4oO2APQxGZxXZUZw8iMXlW+21Oovpy+CrsmcIC4CsIFq+F1wAA+tLT7XawLgoT2ObP9JyjxUT0y8b9DIDz8fre0aOoLtLwoTOH4QMJrPKQR2zKYPGsNFsdXSpipU0pc20qhtv54KzBIBz1W7BH+W6UAIm6l+FgU7Ik89Hp0RGSNxivQysg2tVoEjcfZUDryF5oXLgshQyWCCLJAL4+bZx2OObpED1gEtKdakQjsAlSyA4IpKFKJARimatqKmJTmgcRk0YPi9oO89cryiKIfcj3E6nHYyz4qNXfuV+3TJ15AW2tRZnv+H2a6B8hpeinGPbMJfQBqukgdjqYpbzw88h4YbJHcLz1eUP7kPuwtaFfZbWvEGvVMDdcZFDVK2k73TOTX9G/ynpOtjXl1+Ut1KPj2tHk2E+xQ2Klwxrd5M7xrQGwb4tMznuK2QM74sefKbnjAJ2L2t/ONYW/VWkjh0zAGbWehpRpzKuC/7kf0umpdp/WwOK+VM8U159QKL9o8L5pNwnLkW4XsiJQj2R8yrfG7PK+1vDi+19hZ+YtLNNRw1wFKyp/Lr7RDCn+DJPyKeP/ErVgneSGggBEKjsD4nW6uXzeHw3Jl/kOvyGlnUmiU1oGGpLGpNUAOnwoDirEdXUJcbT+28NJBKuEqqDc4cJQ7dCj+KwFIGxuLk0t4oBOf8oNrKuIqMCxcKm2k25r2GQ88SzHX24smG8kTITQC4QlaKpLeUCDj/LPvlkMVbpRwDznRIxKxTjwrTeR0hwi4miBJE504a2f32ClKAUIEysk/++0C5t3cLOIp09HWu8Y/af3gSXg5xb0z4BeiusEr7kCwe5n5S0itPaiM3qkRHf3XfFPd5wBaaT6ug+4TpKqRoSISGaz8/2lI2qaIGUyAy2brIEVdtSPbbQ58uvkdeb5TLCjuXjPclqR7uGecctIaDffaHPpfvYedU5CPsGyRtqtoQ4LrKgnjHEy35YfQ8NZKC7WZQGirZO5wfx0cqlIoRqEIWva9i27usZrAmBMAazAJSg23XA96qZMD/xtUOWZn0Zh2iqH8pR03MkPKCuG+e8MUWiKyGaY9JQ/tg6U4mTr4TbsfSWJyiX+Q48l34bELrcTNwjDTjl2lTzPFJWr5ARLiRrnudMnoDrxq46SyOCetlZdZOMyL6cDfIs+jWVcOuI0LPKFiLuB6qkUNFoNlHu5ccPjmUo0r7pHMmUv62a2e/TdwlNlrelL5gF40lrPofmh3OF93qvX5Y2EjQGsABECwxFvv17oJrPexcCz2AbuOKQ96uHIK35fcPxdym0QR1rk4rBvvWWSMQxrciYRHMVEZmmoYIAf9utj3St9CTcKAlCaVM0jHFCWbNocvR4zKSzn5111lzxEYYwun1Z/MjzaZvUK0e5SaPCVJxeyZ5XGQnlrKdDvsIxVc5RL0xo/OYm9ZszV5ke2uMJc46luFeLdI0HXFswB7gVwa8UW9CpecqOzmTMbWksDVYrpx/qZzcryaSsbePcKZLlrgNpdAWTA7zuAb3TpeylF4ZjU7oc1ECdxeKKSTSAen3GsLCkq7ioqaY1HhUA04Y/SZ4CtJkGRWhkFM85wxBYbZYovRl05ZATVKWjYa44BhxYCnTGIb2F4ZH2bflOWW1LrvnzFjtWUDN5uwMWldK90tx2KWFkSRjNZLAUs4gAJMTZCD53x4wqOM+wbMoJ4MKS1wJuE7KnJQyCP8KbgHktOg/fPehAIxZ+kg+52rNWQ1SDPwJV0vbE/30biCzIGQ8IhRsybXtQmlgW2JFKT/FC3m3YmaFkT1ELZPz5lvX2x3ZRFZmZaZzleInzhZQrqEZ1oZ+t8p4JehNmQpIKFEe4MiAG6oz2mzEMVcpzxHW7HOgZONqf37Lya5ezWco8TV7Qk057gDfRXs3IGBVTxRChFjvFTYxtOMg8xeoW/rnJQDCXpVwr84dRxdS7G4go8O34EejJEiJBdTH9oqN9xLW8yH85ODLR0gJx5Grn+2yeIUGlnXd7NmT2JBuTUfKvEwLsXrszyIX/X15Fdk81sSuuMS6VlkcX3jSn3Ravu4wOsyGMy1kFfSWCCMF0vBDVWNZ1Dp4nauXpmiGB70gt3BRA7jIc7a/UbJYorlxHEQa7Wz0LQ9RO64363oA/IN7/ft63oIEr7JVtfcx4subvp6OgL7kWvYj+/FJyWtVLvL675hGqm+LNllLxdS3i0B/RqoWJALDaMGGNnLqS07Xv/fsuG2pcvIaLqgGk+BseKWb/z/UCbVhCQR5M97uJOWf92Gk7Gy8HCYDcwl64y3u8Hims9AloidvBecVjQCcix5v65ye/uibnGwxGvxD+KLp26Vn8wP7787pY2JxkF2Y5f9WF8Lkh2YP1v4765SlpABMqhz4LDFf5YbbTrHw/hvhRxserzbPS2FQivshlihZ/LIqGVUkaZWRhuATe7nkCQhJNX1Dib9XPZmNiscjBRO3wUCdR+nG7v6D6gjZ0qWQAucw0cKEPZFxI+fQuKAGHtXt8lpgpSqPBxS/HGYzsi2T2FYwd8lgDAfGJ1XTT1j5GyH/998sVzEdThpj4RWdU1c9EZ9edw2Emk1Qr6ebruE8vn9hiDOAvmE289ah0lB1sANXCIivfKsVq7c6J7J/h/y1t6YsvV6V9KNgNGDSM1jCN7le9SwBv4GuFhTdld1U5VyhO3MA+KqVVdBnxirm8N0UfnzteiXrrC2ysW0XQ8wMnViiqcpuuKuf3MKl0cFClzvF3Jd5r3VXq2mWwP3cmBv3+J3RRcF8wWghsc9RO/cKH7eeIbpINsWd/pnvBI+NrLcc6ii2gkxTMezND2K7k1TrIi1LC1mV7TZqZIc9ir6YNmk+l6p/A0idkRj8n5rDNu+GdafiwsN9X5TH1cpTmBN34vJYAJfHJ4knlP+N2iO3GFjl+v4DB9W1wgxGLOk3x2Uy06T0mjr6nXAYCG3dkASRCl03781DxS2Et8NqjjyCHTuNeyyq0dVUhT2ErEzW0OLHzjL2AP5t9HXmWlGWRbCKZHgdlL3mWr55BLZUsmG6VWs9XwqanmNdK4iuEJm9QfOiN0agOzBjPw80carJD8V5veydoar863vBNZtWXeZ7TzKsj3bUW/Il5FKgXH7DDdX2pw6vzSMIAo/xQzY4gjHh2H+UCNGyhmKqFxp0HQDKX/8nYm7N5h0FFDAAxstk7rW/718jMqEAVmy9SeH3Js7sQ9Bbowalc3DVFaRtakmfzCQlbzHFexVVK757JyAboyM6CqulHr9t5EUbQzNGrgjhSzhvQnIMUxL1gLz3jLxB2RJWeu/2EOCRH8XqN38y29S3BMhEV6t7yq+H2HmjI2zlUJsLytEJ/1I72/Nzzd0Xmq79TDZBPh4ihW9f9nGk1E4mZpeqjRSpRu5Hst+dfAUimZLbvT0A4fF1oPetbwL/I5fEzNpzIwIZhDINZ2ytxZmyxWvkvO7SWZ6MIHyXsWof54vxohi5lhLw0wpCg4bR3dLcSFmTu5zNgWNSPDdpT5mO5Ngt4gWCU3+zh2FHEjRqGgqY6x4v3ud0LieVxBsHHAejDuoiVlmRJ2IPt6gmrlPzextlSt6sAEJNmFq1MeqTwg9kxbZ84IZJcKhUOcKT+9rqRXPmYbz34QAKGk2g7F+ZYKzbLtJunUXFmjdi/m1aXxdTvKv6wfFf60jSAYGi+bQOOfYa0noOZvOoUMEImWXuCx01vLea93owMJ4S19VDTH8nmb2Fk+A4TPz8ZuVv8Hq8bZm7D36FZv10pwjyhpUIQsuPeZhfJdX3CtekJJyXaYiL+o941Ph5Y9/M9V6gP4tl9N5yOyaK7KYzWmShTA89QXFNACXBQQGkN8UIGZ8LsSZ121gkKrwt7s8AzCxxYnY43onpIiXpnK3QbrIdlauv1wrBQO/j78s3QdE9CP6TsbzojDeJaPYHel45KwMYWUA7TdKwBTRb0Sh/aQGvvWE2eXF1JHD6XW5rwsYPjBXeJNw6NdsGyrzfobcPaL+73UNxOV2KLPYaBzYhD20zFr0KnxZiHjlH7DnNW+qSjDK82JXrdPofK6F6Q3GPg3gBrvhMaOkrn7KXYQ53MeuMD4YE2jdssLg0O7Lbt0TzpoV2B76Jnv7USnQVIVLEdTTRDfi/1lrn8fdU/OQtn66Us2r/Rkncf90w6l76Jvq5+OFdoGx6YH+nWhRorfk9UM+H6seXd10VXtilToo1zYH4zDtPphB9S4S/hAClXgbPXBc2exgw36CMPuWURGl7VKw5aSOQB4P2H/SBo/q3p06YzlpDfmw7iyWpHvpHtLAwAMltTnuZIWwMwBS6RrJ3XmoqzP1jn6JeBsBNPFwYjd2Y+sSGVhu1a0UN6Rhs34bAbYlmCvP/CW/ctKhzdvy8DqJgGptVyZXqD1yVZWcK9hCV8lOrpM3Rb/ZWCV769px4K4sPh0FB1nbcgqkpETd2y+20ryN4dNDoi+Y5kg84awrcyBwSsQpuW8SQHFHyvqRTeTV0uXGPiAFupXB+euVF3vPob1Wf2bhIXmT68rNt6wx9SzGBLJF31dMUjS/8trtzL2dybt+vZ+mMvoM3qkcUzzdvCGn8xKPniCTP5eckLj4lG4Gx/jRx0VmYQOo0vutTlfd+cBDrhZDIqxCgzp4iZHC3SqW33mlFg/HDMMp31/JE+86beAT235mzUhDTTj8DtLqi+VEBWXGv6HIANDici1H0pw7aHWz/4MpFXN5VM6Ycyc7Ng8r4cNKFyWXfg2ei/OQygxIGRvX6ybXmVOwbxuWP74BxzV2HUyJNY2HNsMPfJVV6IbIXP1S2R2dca0ROGO3mQ3eceuDV1mNq8bboDF4lOSwpcnRk5R5dFLmNeb0L5zyXKDP/jnpFnn+Xq8XAEFjBSb2azljhHo0n2tCKy5F72gle9AtkFwmofaCPpsVyv0zur1O+lR8ktBp4a7DfSDKd9STfx27AlWBhx6K+aZterIk0/SzBV9M8XFRHbC0MHcHXxYBRVXQxrnlrOgluDjygC2EVxMD+O+heLLsFbJ14aEky9v8aq9j3zDIA41bfiGKQOlSUZAvE9na1V1/cAzhYze25FEVZxeMS+v+9n9mHJBJU759+bGPBYkh4qs+BuxZY/XUch2QpO1z6nIaf2hFFE5F9E3FPf2z9bHAfRD12LBz80dGaaxod8W3pgFMumMQqhYOtovmIgFpLbKywi6jH/JAyDtantdc79rtJvwtdrUznOaUuzihBltAW3Q8u27UhAxf1gZtEdjpYVyCqryyC97CZHeRXpHFUlTZ0IGJzSKpMxFjiGlNBMlKFp8xu6wgeR8ca3t/MU8GdX8bloeysRDKLWfDa+2PW8UOvGNCW7WaiKMje7tU7/J8u5WOKcA/OLy3na+NUm/JA23qLdADmVKPg8lB0zP5vyo/PXpvMlSxPm6VVGE0actSosviAoky+tF19/MkLZsnC728tElQ1m021HKTPCjFqwDsx2LKZPjS3sIYkVjMVK0O3OzpFpFIlyGJClOtsv8og8iCnxBulHC5ZCokg+OGdGwviEYyftJyhC/vzGMhynHJnFeX0gH+QvDKzSPae4PU04X6KOCoz4+ZS39usXMffkgZfVXQQic2gW2Ly9PALfmRi+2DJiiIUcENGqlJdnTIYydnj14A8qprWR8vgG/roapwdhCLefYmdXisYww/DaEoeLbaxISerLMDJTYm+imaGPQ3oBxHV4lYFSaE+NFt8irbBYfwcLf9ofUbRw9cCfIt2QGNFufnRAAc1JTZTnXHQQw=="
decrypt(data)